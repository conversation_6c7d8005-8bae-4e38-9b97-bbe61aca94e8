import * as React from 'react';
import { EventReporter } from '@avanade-teams/app-insights-reporter';
import { IUserChatItem, FetchUserChatsAndChannelsPaginated } from '../accessors/useUserChatsAndChannelsAccessor';
import { UseTeamsChatsApiReturnType } from '../accessors/useTeamsChatsApiAccessor';
import useTeamsChatsRepositoryAccessor from '../accessors/useTeamsChatsRepositoryAccessor';
import useRemoteTeamsChatsFeature from './useRemoteTeamsChatsFeature';
import { ITeamsChatsItem, DbProvider } from '../../types/IGeraniumAttaneDB';

export interface UseTeamsSettingDataProps {
  fetchUserChatsAndChannels?: () => Promise<IUserChatItem[]>;
  fetchUserChatsAndChannelsPaginated?: FetchUserChatsAndChannelsPaginated;
  getTeamsChatsApi?: UseTeamsChatsApiReturnType['getTeamsChatsApi'];
  postTeamsChatsApi?: UseTeamsChatsApiReturnType['postTeamsChatsApi'];
  deleteTeamsChatsApi?: UseTeamsChatsApiReturnType['deleteTeamsChatsApi'];
  isModalOpen: boolean;
  openDB?: DbProvider;
  eventReporter: EventReporter;
}

export interface UseTeamsSettingDataReturnType {
  // データ状態
  allChatItems: IUserChatItem[];
  savedItems: Set<string>;
  savedItemsDetails: IUserChatItem[];
  isLoadingSavedItems: boolean;
  // ページネーション状態
  hasMoreChats: boolean;
  nextPageToken?: string;
  isLoadingMore: boolean;
  // データ操作関数
  loadSavedItems: () => Promise<void>;
  saveSelectedItems: (selectedItems: Set<string>, allChatItems: IUserChatItem[]) => Promise<void>;
  loadMoreChats: () => Promise<void>;
  // 状態更新関数
  setAllChatItems: React.Dispatch<React.SetStateAction<IUserChatItem[]>>;
  setSavedItems: React.Dispatch<React.SetStateAction<Set<string>>>;
}

/**
 * Teams設定のデータ取得・保存ロジックを管理するカスタムフック
 */
const useTeamsSettingData = (props: UseTeamsSettingDataProps): UseTeamsSettingDataReturnType => {
  const {
    fetchUserChatsAndChannels,
    fetchUserChatsAndChannelsPaginated,
    getTeamsChatsApi,
    postTeamsChatsApi,
    deleteTeamsChatsApi,
    isModalOpen,
    openDB,
    eventReporter,
  } = props;

  // リポジトリアクセサーとリモート機能を初期化
  const repositoryReturn = useTeamsChatsRepositoryAccessor(openDB);
  const apiReturn = { getTeamsChatsApi, postTeamsChatsApi, deleteTeamsChatsApi };
  const remoteFeature = useRemoteTeamsChatsFeature(repositoryReturn, apiReturn, eventReporter);

  const { addRemoteTeamsChats, deleteRemoteTeamsChats } = remoteFeature;

  // データ状態管理
  const [allChatItems, setAllChatItems] = React.useState<IUserChatItem[]>([]);
  const [savedItems, setSavedItems] = React.useState<Set<string>>(new Set());
  const [savedItemsDetails, setSavedItemsDetails] = React.useState<IUserChatItem[]>([]);
  const [isLoadingSavedItems, setIsLoadingSavedItems] = React.useState(false);

  // ページネーション状態管理
  const [hasMoreChats, setHasMoreChats] = React.useState(false);
  const [nextPageToken, setNextPageToken] = React.useState<string | undefined>(undefined);
  const [isLoadingMore, setIsLoadingMore] = React.useState(false);

  // 保存済みアイテムを取得する関数
  const loadSavedItems = React.useCallback(async () => {
    if (!getTeamsChatsApi) return;

    setIsLoadingSavedItems(true);
    try {
      const savedTeamsChats = await getTeamsChatsApi();
      const savedItemIds = new Set(
        savedTeamsChats
          .map((item) => item.chatId || item.channelId)
          .filter((id): id is string => Boolean(id)),
      );
      setSavedItems(savedItemIds);

      // IndexedDBから保存済みアイテムの詳細情報を取得
      if (repositoryReturn.retrieveTeamsChats) {
        try {
          const savedItemsFromDB = await repositoryReturn.retrieveTeamsChats();
          // ITeamsChatsItemをIUserChatItemに変換
          const savedItemsDetailsConverted: IUserChatItem[] = savedItemsFromDB.map((item) => ({
            id: item.id,
            name: item.name,
            type: item.type,
            chatType: item.chatType,
            teamId: item.teamId,
          }));
          setSavedItemsDetails(savedItemsDetailsConverted);
        } catch (dbError) {
          // IndexedDBからの取得に失敗した場合は空配列を設定
          setSavedItemsDetails([]);
        }
      }
    } catch (loadError) {
      throw new Error(`保存済みアイテムの取得に失敗しました: ${loadError}`);
    } finally {
      setIsLoadingSavedItems(false);
    }
  }, [getTeamsChatsApi, repositoryReturn]);

  // 選択されたアイテムを保存する関数
  const saveSelectedItems = React.useCallback(async (
    selectedItems: Set<string>,
    currentAllChatItems: IUserChatItem[],
  ) => {
    if (!addRemoteTeamsChats || !deleteRemoteTeamsChats) {
      throw new Error('リモート機能が利用できません');
    }

    // 削除対象のアイテムを特定（保存済みだが選択されていないアイテム）
    const itemsToDeleteNow = Array.from(savedItems).filter((id) => !selectedItems.has(id));

    // 1. 削除処理を先に実行
    if (itemsToDeleteNow.length > 0) {

      // 削除対象のTeamsChatsアイテムを作成
      const itemsToDelete = itemsToDeleteNow.map((chatId) => {
        const originalItem = currentAllChatItems.find((item) => item.id === chatId);
        if (!originalItem) {
          throw new Error(`削除対象アイテムが見つかりません: ${chatId}`);
        }

        return {
          id: originalItem.id,
          name: originalItem.name,
          type: originalItem.type,
          chatType: originalItem.chatType,
          teamId: originalItem.teamId,
          countId: 0, // 削除時は不要
        } as ITeamsChatsItem;
      });

      // キューを使用して削除
      await Promise.all(
        itemsToDelete.map(async (item) => {
          try {
            await deleteRemoteTeamsChats(item);
          } catch (deleteError) {
            throw new Error(`Delete queue failed for item ${item.id}: ${deleteError}`);
          }
        }),
      );
    }

    // 2. 選択されたアイテムをすべてUpsert（新規追加または更新）
    if (selectedItems.size > 0) {
      // 選択されたアイテムを取得
      const selectedChatItems = currentAllChatItems.filter((item) => selectedItems.has(item.id));

      // TeamsChatsアイテムに変換（countIdは順番に割り当て）
      const teamsChatsItems = selectedChatItems.map((item, index) => ({
        id: item.id,
        name: item.name,
        type: item.type,
        chatType: item.chatType,
        teamId: item.teamId,
        countId: index + 1,
      } as ITeamsChatsItem));

      // キューを使用して追加
      await Promise.all(
        teamsChatsItems.map(async (item) => {
          try {
            await addRemoteTeamsChats(item);
          } catch (apiError) {
            throw new Error(`Add queue failed for item ${item.countId}: ${apiError}`);
          }
        }),
      );
    }

    // 保存済みアイテムを更新
    setSavedItems(new Set(selectedItems));

    // 保存済みアイテムの詳細情報を更新
    const selectedChatItemsForDetails = currentAllChatItems.filter(
      (item) => selectedItems.has(item.id),
    );
    setSavedItemsDetails(selectedChatItemsForDetails);
  }, [addRemoteTeamsChats, deleteRemoteTeamsChats, savedItems]);

  // さらに読み込む関数
  const loadMoreChats = React.useCallback(async () => {
    if (!fetchUserChatsAndChannelsPaginated || !nextPageToken || isLoadingMore) {
      return;
    }

    setIsLoadingMore(true);
    try {
      const result = await fetchUserChatsAndChannelsPaginated(nextPageToken);
      setAllChatItems((prev) => [...prev, ...result.items]);
      setHasMoreChats(result.hasMore);
      setNextPageToken(result.nextPageToken);
    } catch {
      // エラー時は何もしない（現在の状態を維持）
    } finally {
      setIsLoadingMore(false);
    }
  }, [fetchUserChatsAndChannelsPaginated,
    nextPageToken,
    isLoadingMore]);

  // データ取得のEffect（ページネーション対応）
  React.useEffect(() => {
    if (isModalOpen) {
      // デバッグ用：実行される条件分岐を確認
      if (process.env.NODE_ENV === 'development') {
        // eslint-disable-next-line no-console
        console.log('TeamsSettingData: Modal opened, checking data fetch options', {
          fetchUserChatsAndChannelsPaginated: !!fetchUserChatsAndChannelsPaginated,
          fetchUserChatsAndChannels: !!fetchUserChatsAndChannels,
        });
      }

      // ページネーション機能がある場合は初回20件のみ取得
      if (fetchUserChatsAndChannelsPaginated) {
        Promise.all([
          // 初回20件のチャット・チャネル一覧
          fetchUserChatsAndChannelsPaginated(),
          // 保存済みアイテム取得
          loadSavedItems(),
        ])
          .then(([result]) => {
            setAllChatItems(result.items);
            setHasMoreChats(result.hasMore);
            setNextPageToken(result.nextPageToken);
          })
          .catch((error) => {
            // エラー時は空の状態を設定
            setAllChatItems([]);
            setHasMoreChats(false);
            setNextPageToken(undefined);
            // デバッグ用：エラーをコンソールに出力
            if (process.env.NODE_ENV === 'development') {
              // eslint-disable-next-line no-console
              console.error('fetchUserChatsAndChannelsPaginated error:', error);
            }
          });
      } else if (fetchUserChatsAndChannels) {
        // 従来の全件取得（後方互換性のため）
        Promise.all([
          fetchUserChatsAndChannels(),
          loadSavedItems(),
        ])
          .then(([items]) => {
            setAllChatItems(items);
            setHasMoreChats(false);
            setNextPageToken(undefined);
          })
          .catch(() => {
            // エラー時は空の状態を設定
            setAllChatItems([]);
            setHasMoreChats(false);
            setNextPageToken(undefined);
          });
      }
    }
  }, [isModalOpen, fetchUserChatsAndChannels, fetchUserChatsAndChannelsPaginated, loadSavedItems]);

  return {
    // データ状態
    allChatItems,
    savedItems,
    savedItemsDetails,
    isLoadingSavedItems,
    // ページネーション状態
    hasMoreChats,
    nextPageToken,
    isLoadingMore,

    // データ操作関数
    loadSavedItems,
    saveSelectedItems,
    loadMoreChats,

    // 状態更新関数
    setAllChatItems,
    setSavedItems,
  };
};

export default useTeamsSettingData;
